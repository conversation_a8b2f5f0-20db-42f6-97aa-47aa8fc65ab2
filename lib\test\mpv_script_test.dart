import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:cat_tv/utils/mpv_initializer.dart';

Future<void> runMpvScriptTest() async {
  if (!Platform.isWindows) {
    if (kDebugMode) {
      print('MPV script test is only supported on Windows.');
    }
    return;
  }

  if (MpvInitializer.mpvPath == null) {
    if (kDebugMode) {
      print('MPV not initialized. Run MpvInitializer.initialize() first.');
    }
    return;
  }

  final videoTracks = [
    {"id": 1, "desc": "426x240 @ 911 kbps"},
    {"id": 2, "desc": "640x360 @ 1022 kbps"},
    {"id": 3, "desc": "1280x720 @ 1955 kbps"},
    {"id": 4, "desc": "1920x1080 @ 2661 kbps"},
  ];

  final audioTracks = [
    {"id": 1, "desc": "aac 2ch 48000 Hz @ 911 kbps"},
    {"id": 2, "desc": "aac 2ch 48000 Hz @ 1022 kbps"},
    {"id": 3, "desc": "aac 2ch 48000 Hz @ 1955 kbps"},
    {"id": 4, "desc": "aac 2ch 48000 Hz @ 2661 kbps"},
  ];

  jsonEncode(videoTracks);
  jsonEncode(audioTracks);

  Process? mpvProcess;

  try {
    // Build command arguments (minimal config needed to load script)
    final args = <String>[];
    final scriptPaths = MpvInitializer.scriptPaths;
    if (scriptPaths.isNotEmpty) {
      args.add('--script=${scriptPaths.values.join(',')}');
    }
    // Add a dummy URL or just start mpv to load scripts
    args.add('--no-video'); // Don't need video for this test
    args.add('--no-audio'); // Don't need audio for this test
    args.add('--idle'); // Keep MPV running after initialization

    if (kDebugMode) {
      print(
        '[MpvScriptTest] Starting mpv with path: ${MpvInitializer.mpvPath}',
      );
      print('[MpvScriptTest] Arguments: $args');
    }

    // Get the directory containing mpv.exe
    final mpvDir = Directory(MpvInitializer.mpvPath!).parent.path;

    // Start the mpv process
    mpvProcess = await Process.start(
      MpvInitializer.mpvPath!,
      args,
      mode: ProcessStartMode.normal,
      workingDirectory: mpvDir,
      runInShell: true,
    );

    // Listen for stdout

    // Listen for stderr
    mpvProcess.stderr.transform(const SystemEncoding().decoder).listen((data) {
      if (kDebugMode) {
        print('MPV stderr: $data');
      }
    });

    // Listen for process exit
    mpvProcess.exitCode.then((exitCode) {
      if (kDebugMode) {
        print('[MpvScriptTest] mpv process exited with code: $exitCode');
      }
    });
  } catch (e) {
    if (kDebugMode) {
      print('[MpvScriptTest] Error starting mpv test: $e');
    }
  }

  // Note: This test will keep MPV running in idle mode.
  // You may need to manually close the MPV window or the application
  // to terminate the process.
}

// To run this test, you would typically call runMpvScriptTest()
// from a suitable entry point in your test environment after
// MpvInitializer.initialize() has been called.
