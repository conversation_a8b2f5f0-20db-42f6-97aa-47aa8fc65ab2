import 'package:flutter/material.dart';
import 'package:cat_tv/utils/mpv_quality_controller.dart';

class QualityDisplayWidget extends StatelessWidget {
  final MpvQualityController qualityController;

  const QualityDisplayWidget({super.key, required this.qualityController});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<VideoTrack>>(
      stream: qualityController.videoTracksStream,
      builder: (context, snapshot) {
        if (snapshot.hasData && snapshot.data!.isNotEmpty) {
          return Padding(
            padding: const EdgeInsets.all(4.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Available Qualities:',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12.0, // Reduced font size
                  ),
                ),
                ...snapshot.data!.map(
                  (track) => Text(
                    track.displayName,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10.0,
                    ), // Reduced font size
                  ),
                ),
              ],
            ),
          );
        } else {
          return const SizedBox.shrink(); // Don't show anything if no data
        }
      },
    );
  }
}
