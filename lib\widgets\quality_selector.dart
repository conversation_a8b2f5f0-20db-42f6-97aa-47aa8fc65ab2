import 'package:flutter/material.dart';
import '../utils/mpv_quality_controller.dart';

class QualitySelector extends StatefulWidget {
  final MpvQualityController controller;
  final bool showVideoTracks;
  final bool showAudioTracks;
  final VoidCallback? onClose;

  const QualitySelector({
    super.key,
    required this.controller,
    this.showVideoTracks = true,
    this.showAudioTracks = true,
    this.onClose,
  });

  @override
  State<QualitySelector> createState() => _QualitySelectorState();
}

class _QualitySelectorState extends State<QualitySelector> {
  int? _selectedVideoTrackId;
  int? _selectedAudioTrackId;
  List<VideoTrack> _videoTracks = [];
  List<AudioTrack> _audioTracks = [];

  @override
  void initState() {
    super.initState();
    _loadTracks();

    // Listen for track changes
    widget.controller.videoTracksStream.listen((tracks) {
      setState(() {
        _videoTracks = tracks;
        _selectedVideoTrackId =
            tracks.firstWhere((t) => t.selected, orElse: () => tracks.first).id;
      });
    });

    widget.controller.audioTracksStream.listen((tracks) {
      setState(() {
        _audioTracks = tracks;
        _selectedAudioTrackId =
            tracks.firstWhere((t) => t.selected, orElse: () => tracks.first).id;
      });
    });
  }

  void _loadTracks() {
    _videoTracks = widget.controller.videoTracks;
    _audioTracks = widget.controller.audioTracks;

    if (_videoTracks.isNotEmpty) {
      _selectedVideoTrackId =
          _videoTracks
              .firstWhere((t) => t.selected, orElse: () => _videoTracks.first)
              .id;
    }

    if (_audioTracks.isNotEmpty) {
      _selectedAudioTrackId =
          _audioTracks
              .firstWhere((t) => t.selected, orElse: () => _audioTracks.first)
              .id;
    }

    // Request tracks from MPV if we don't have any
    if (_videoTracks.isEmpty || _audioTracks.isEmpty) {
      widget.controller.requestTracks();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(8), // Reduced padding
      decoration: BoxDecoration(
        // ignore: deprecated_member_use
        color: Colors.black.withOpacity(0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Quality Settings',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16, // Reduced font size
                  fontWeight: FontWeight.bold,
                ),
              ),
              IconButton(
                icon: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 20,
                ), // Reduced icon size
                onPressed: widget.onClose,
              ),
            ],
          ),
          const SizedBox(height: 12), // Reduced spacing
          if (widget.showVideoTracks) ...[
            const Text(
              'Video Quality',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14, // Reduced font size
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 6), // Reduced spacing
            _buildVideoTrackSelector(),
            const SizedBox(height: 12), // Reduced spacing
          ],
          if (widget.showAudioTracks) ...[
            const Text(
              'Audio Quality',
              style: TextStyle(
                color: Colors.white,
                fontSize: 14, // Reduced font size
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 6), // Reduced spacing
            _buildAudioTrackSelector(),
            const SizedBox(height: 12), // Reduced spacing
          ],
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton(
                onPressed: _applySettings,
                child: const Text('Apply'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVideoTrackSelector() {
    if (_videoTracks.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(4.0), // Reduced padding
          child: Text(
            'No video tracks available',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ), // Reduced font size
          ),
        ),
      );
    }

    return Column(
      children:
          _videoTracks.map((track) {
            return RadioListTile<int>(
              title: Text(
                '${track.resolution} (${track.bitrate} kbps)',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ), // Reduced font size
              ),
              subtitle: Text(
                '${track.codec} - ${track.fps} fps',
                style: const TextStyle(
                  color: Colors.white70,
                  fontSize: 10,
                ), // Reduced font size
              ),
              value: track.id,
              groupValue: _selectedVideoTrackId,
              onChanged: (value) {
                setState(() {
                  _selectedVideoTrackId = value;
                });
              },
              activeColor: Colors.blue,
              dense: true,
            );
          }).toList(),
    );
  }

  Widget _buildAudioTrackSelector() {
    if (_audioTracks.isEmpty) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(4.0), // Reduced padding
          child: Text(
            'No audio tracks available',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ), // Reduced font size
          ),
        ),
      );
    }

    return Column(
      children:
          _audioTracks.map((track) {
            return RadioListTile<int>(
              title: Text(
                '${track.codec} ${track.channels}ch (${track.bitrate} kbps)',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ), // Reduced font size
              ),
              value: track.id,
              groupValue: _selectedAudioTrackId,
              onChanged: (value) {
                setState(() {
                  _selectedAudioTrackId = value;
                });
              },
              activeColor: Colors.blue,
              dense: true,
            );
          }).toList(),
    );
  }

  void _applySettings() {
    if (_selectedVideoTrackId != null) {
      widget.controller.setVideoTrack(_selectedVideoTrackId!);
    }

    if (_selectedAudioTrackId != null) {
      widget.controller.setAudioTrack(_selectedAudioTrackId!);
    }

    if (widget.onClose != null) {
      widget.onClose!();
    }
  }
}
