import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class WebViewPage extends StatefulWidget {
  const WebViewPage({super.key});

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  InAppWebViewController? _webViewController;
  final TextEditingController _urlController = TextEditingController();
  bool _shouldBlockAds = true;

  // Basic ad blocking patterns
  final List<String> _adBlockPatterns = [
    'doubleclick.net',
    'googleadservices.com',
    'googlesyndication.com',
    'googletagmanager.com',
    'google-analytics.com',
    'facebook.com/tr',
    'amazon-adsystem.com',
    'adsystem.amazon.com',
    'ads.yahoo.com',
    'advertising.com',
    'adsystem.com',
    'adsense.com',
    'adnxs.com',
    'adsystem.amazon.com',
    'amazon-adsystem.com',
    'ads.twitter.com',
    'analytics.twitter.com',
    'ads.linkedin.com',
    'ads.pinterest.com',
    'ads.reddit.com',
    'outbrain.com',
    'taboola.com',
    'scorecardresearch.com',
    'quantserve.com',
    'adsystem',
    'advertising',
    'googleads',
    'adsense',
    'adservice',
    'adserver',
    'adnetwork',
    'adsystem',
    'adnxs',
    'adsystem',
    'ads.',
    '/ads/',
    'advertisement',
    'popup',
    'popunder',
  ];

  bool _shouldBlockUrl(String url) {
    if (!_shouldBlockAds) return false;

    final lowerUrl = url.toLowerCase();
    return _adBlockPatterns.any((pattern) => lowerUrl.contains(pattern));
  }

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  void _loadUrl() {
    final url = _urlController.text.trim();
    if (url.isNotEmpty) {
      _webViewController?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _urlController,
          decoration: const InputDecoration(
            hintText: 'Enter URL to embed',
            border: InputBorder.none,
          ),
          onSubmitted: (_) => _loadUrl(),
        ),
        actions: [
          // Ad blocking toggle
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _shouldBlockAds ? Icons.shield : Icons.shield_outlined,
                color: _shouldBlockAds ? Colors.green : Colors.grey,
                size: 20,
              ),
              Switch(
                value: _shouldBlockAds,
                onChanged: (value) {
                  setState(() {
                    _shouldBlockAds = value;
                  });
                },
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ],
          ),
          IconButton(icon: const Icon(Icons.play_arrow), onPressed: _loadUrl),
        ],
      ),
      body: InAppWebView(
        initialUrlRequest: URLRequest(url: WebUri('about:blank')),
        initialSettings: InAppWebViewSettings(
          javaScriptEnabled: true,
          mediaPlaybackRequiresUserGesture: false,
          domStorageEnabled: true,
          databaseEnabled: true,
          // Prevent JavaScript from opening new windows (popups) automatically.
          javaScriptCanOpenWindowsAutomatically: false,
        ),
        // Intercept and block ad requests
        shouldOverrideUrlLoading: (controller, navigationAction) async {
          final url = navigationAction.request.url.toString();
          if (_shouldBlockUrl(url)) {
            if (kDebugMode) {
              print("Blocking ad request: $url");
            }
            return NavigationActionPolicy.CANCEL;
          }
          return NavigationActionPolicy.ALLOW;
        },
        // Intercept and block requests to create new windows (popups).
        onCreateWindow: (controller, createWindowRequest) async {
          if (kDebugMode) {
            print("Blocking popup: ${createWindowRequest.request.url}");
          }
          return false; // Return false to block the window from being created
        },
        onWebViewCreated: (controller) {
          _webViewController = controller;
        },
        onLoadStart: (controller, url) {
          if (kDebugMode) {
            print("WebView started loading: $url");
          }
        },
        onLoadStop: (controller, url) async {
          if (kDebugMode) {
            print("WebView finished loading: $url");
          }
        },
        onReceivedError: (controller, request, error) {
          if (kDebugMode) {
            print(
              "Error loading ${request.url}: ${error.description} (Code: ${error.type})",
            );
          }
        },
        onConsoleMessage: (controller, consoleMessage) {
          if (kDebugMode) {
            print("Console Message: ${consoleMessage.message}");
          }
        },
      ),
    );
  }
}
