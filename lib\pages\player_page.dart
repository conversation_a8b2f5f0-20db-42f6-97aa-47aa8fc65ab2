import 'package:flutter/material.dart';
import 'package:adblocker_webview/adblocker_webview.dart';

class PlayerPage extends StatefulWidget {
  const PlayerPage({super.key});

  @override
  State<PlayerPage> createState() => _PlayerPageState();
}

class _PlayerPageState extends State<PlayerPage> {
  final TextEditingController _urlController = TextEditingController();
  bool _shouldBlockAds = true;
  Uri? _currentUrl;
  bool _isLoading = false;
  int _loadingProgress = 0;

  @override
  void dispose() {
    _urlController.dispose();
    super.dispose();
  }

  void _loadUrl() {
    final url = _urlController.text.trim();
    if (url.isNotEmpty) {
      try {
        setState(() {
          _currentUrl = Uri.parse(url);
          _isLoading = true;
          _loadingProgress = 0;
        });
      } catch (e) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Invalid URL: $e')));
      }
    }
  }

  void _goBack() async {
    final controller = AdBlockerWebviewController.instance;
    if (await controller.canGoBack()) {
      controller.goBack();
    }
  }

  void _goForward() async {
    final controller = AdBlockerWebviewController.instance;
    if (await controller.canGoForward()) {
      controller.goForward();
    }
  }

  void _refresh() {
    AdBlockerWebviewController.instance.reload();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: TextField(
          controller: _urlController,
          decoration: const InputDecoration(
            hintText: 'Enter URL',
            border: InputBorder.none,
          ),
          onSubmitted: (_) => _loadUrl(),
        ),
        actions: [
          // Ad blocking toggle
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _shouldBlockAds ? Icons.shield : Icons.shield_outlined,
                color: _shouldBlockAds ? Colors.green : Colors.grey,
                size: 20,
              ),
              Switch(
                value: _shouldBlockAds,
                onChanged: (value) {
                  setState(() {
                    _shouldBlockAds = value;
                  });
                },
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: _goBack,
            tooltip: 'Go Back',
          ),
          IconButton(
            icon: const Icon(Icons.arrow_forward),
            onPressed: _goForward,
            tooltip: 'Go Forward',
          ),
          IconButton(
            icon: const Icon(Icons.play_arrow),
            onPressed: _loadUrl,
            tooltip: 'Load URL',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refresh,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Loading progress bar
          if (_isLoading)
            LinearProgressIndicator(
              value: _loadingProgress / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).primaryColor,
              ),
            ),
          // WebView
          Expanded(
            child:
                _currentUrl != null
                    ? AdBlockerWebview(
                      url: _currentUrl!,
                      shouldBlockAds: _shouldBlockAds,
                      adBlockerWebviewController:
                          AdBlockerWebviewController.instance,
                      onLoadStart: (url) {
                        setState(() {
                          _isLoading = true;
                          _loadingProgress = 0;
                          _urlController.text = url.toString();
                        });
                      },
                      onLoadFinished: (url) {
                        setState(() {
                          _isLoading = false;
                          _loadingProgress = 100;
                        });
                      },
                      onProgress: (progress) {
                        setState(() {
                          _loadingProgress = progress;
                        });
                      },
                      onLoadError: (url, code) {
                        setState(() {
                          _isLoading = false;
                        });
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Failed to load $url (Error: $code)'),
                          ),
                        );
                      },
                      onUrlChanged: (url) {
                        setState(() {
                          _urlController.text = url.toString();
                        });
                      },
                    )
                    : const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.web, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text(
                            'Enter a URL to start browsing',
                            style: TextStyle(fontSize: 18, color: Colors.grey),
                          ),
                          SizedBox(height: 8),
                          Text(
                            'Ad blocking is enabled by default',
                            style: TextStyle(fontSize: 14, color: Colors.grey),
                          ),
                        ],
                      ),
                    ),
          ),
        ],
      ),
    );
  }
}
