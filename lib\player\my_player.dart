import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:cat_tv/utils/mpv_initializer.dart';
import 'package:cat_tv/utils/mpv_output_parser.dart';
import 'package:cat_tv/utils/mpv_quality_controller.dart';
import 'package:cat_tv/utils/named_pipe_client_windows.dart';

class VideoParams {
  final double dw;
  final double dh;

  VideoParams({this.dw = 0, this.dh = 0});
}

enum PlayerState { idle, loading, playing, error, closed }

class MyPlayer {
  final Map<String, String> config;
  final _id = ValueNotifier<int>(0);
  final _videoParams = ValueNotifier<VideoParams>(VideoParams());
  final _playerState = ValueNotifier<PlayerState>(PlayerState.idle);
  final _errorMessage = ValueNotifier<String?>(null);

  Process? _process;
  NamedPipeClientWindows? _ipcClient; // Named pipe client for IPC communication
  bool _isWindows = false;
  StreamSubscription? _stdoutSubscription;
  StreamSubscription? _stderrSubscription;
  Timer? _watchdogTimer;
  bool _hasReceivedOutput = false;
  final MpvOutputParser _outputParser = MpvOutputParser();
  bool _scriptReady = false; // Flag to indicate if the script is ready

  // Quality controller
  late final MpvQualityController _qualityController;

  // Getter for quality controller
  MpvQualityController get qualityController => _qualityController;

  MyPlayer({required this.config}) {
    _isWindows = Platform.isWindows;
    // Initialize quality controller with a function to send commands to MPV
    _qualityController = MpvQualityController(_sendCommand);
  }

  // Method to send commands to MPV
  void _sendCommand(String command) {
    if (_ipcClient != null) {
      try {
        if (kDebugMode) {
          print('[MyPlayer] Sending command: $command');
        }
        _ipcClient!.write(command);
      } catch (e) {
        if (kDebugMode) {
          print('[MyPlayer] Error sending command: $e');
        }
      }
    } else {
      if (kDebugMode) {
        print('[MyPlayer] IPC client not connected. Cannot send command.');
      }
    }
  }

  ValueNotifier<int> get id => _id;
  ValueNotifier<VideoParams> get videoParams => _videoParams;
  ValueNotifier<PlayerState> get playerState => _playerState;
  ValueNotifier<String?> get errorMessage => _errorMessage;

  Future<void> open(String url) async {
    if (_process != null) {
      await dispose();
    }

    _playerState.value = PlayerState.loading;
    _errorMessage.value = null;
    _hasReceivedOutput = false;
    _outputParser.reset(); // Reset the parser for a new stream
    _scriptReady = false; // Reset script ready flag

    // Set up the quality controller with the output parser
    _outputParser.setQualityController(_qualityController);
    // Set the output parser reference in the quality controller
    _qualityController.outputParser = _outputParser;

    if (_isWindows && MpvInitializer.mpvPath != null) {
      await _openWithMpvOnWindows(url);
    } else {
      // Fallback or implementation for other platforms
      if (kDebugMode) {
        print('Platform not supported or mpv not initialized');
      }
      _setError('Platform not supported or mpv not initialized');
    }
  }

  Future<void> _openWithMpvOnWindows(String url) async {
    try {
      // Build command arguments
      final args = <String>[];

      // Add config options
      config.forEach((key, value) {
        args.add('--$key=$value');
      });

      // Add the URL to play
      args.add(url);

      if (kDebugMode) {
        print('Starting mpv with path: ${MpvInitializer.mpvPath}');
        print('Arguments: $args');
      }

      // Get the directory containing mpv.exe
      final mpvDir = Directory(MpvInitializer.mpvPath!).parent.path;

      // Start the mpv process
      _process = await Process.start(
        MpvInitializer.mpvPath!,
        args,
        mode: ProcessStartMode.normal,
        workingDirectory: mpvDir,
        runInShell: false, // Changed to false
      );

      // Set a dummy texture ID for now
      _id.value = 1;

      // Set default video parameters
      _videoParams.value = VideoParams(dw: 1280, dh: 720);

      // Connect to the IPC socket after starting the process
      await _connectToIpcSocket();

      // Listen for stdout
      _stdoutSubscription = _process!.stdout
          .transform(const SystemEncoding().decoder)
          .listen((data) {
            if (kDebugMode) {
              print('MPV stdout: $data');
            }
            _hasReceivedOutput = true;

            // Process each line of the output using the parser
            final lines = data.split('\n');
            for (final line in lines) {
              _outputParser.parseLine(line);

              // Check if the script is ready
              if (!_scriptReady && line.contains('[QualitySelector] Ready')) {
                _scriptReady = true;
                if (kDebugMode) {
                  print('[MyPlayer] Script is ready.');
                }
                // Request tracks after the script is ready
                _qualityController.requestTracks();
              }
            }

            // Check for playback started
            if (data.contains('AV:') || data.contains('Starting playback')) {
              _playerState.value = PlayerState.playing;
            }
          });

      // Listen for stderr
      _stderrSubscription = _process!.stderr
          .transform(const SystemEncoding().decoder)
          .listen((data) {
            if (kDebugMode) {
              print('MPV stderr: $data');
            }
            _hasReceivedOutput = true;

            // Process each line of the output
            final lines = data.split('\n');
            for (final line in lines) {
              // Check for common errors
              if (line.contains('Failed to open') ||
                  line.contains('Error') ||
                  line.contains('Failed to recognize file format')) {
                _setError('Failed to play stream: $data');
              }
            }
          });

      // Start watchdog timer to detect if MPV is not responding
      _watchdogTimer = Timer(const Duration(seconds: 10), () {
        if (!_hasReceivedOutput && _playerState.value == PlayerState.loading) {
          _setError('Stream failed to load within timeout period');
        }
      });

      // Listen for process exit
      _process!.exitCode.then((exitCode) {
        if (kDebugMode) {
          print('mpv process exited with code: $exitCode');
        }
        _playerState.value = PlayerState.closed;
      });
    } catch (e) {
      _setError('Error starting mpv: $e');
    }
  }

  // Method to connect to the IPC socket
  Future<void> _connectToIpcSocket() async {
    if (!_isWindows) return;

    final socketPath = config['input-ipc-server'];
    if (socketPath == null) {
      if (kDebugMode) {
        print('[MyPlayer] IPC socket path not configured.');
      }
      return;
    }

    _ipcClient = NamedPipeClientWindows(socketPath);

    // Attempt to connect with retries
    const int maxRetries = 10;
    const Duration retryDelay = Duration(milliseconds: 500);

    for (int i = 0; i < maxRetries; i++) {
      if (kDebugMode) {
        print(
          '[MyPlayer] Attempting to connect to IPC socket (Retry ${i + 1}/$maxRetries)...',
        );
      }
      if (await _ipcClient!.connect()) {
        if (kDebugMode) {
          print('[MyPlayer] Successfully connected to IPC socket.');
        }
        return;
      }
      await Future.delayed(retryDelay);
    }

    if (kDebugMode) {
      print(
        '[MyPlayer] Failed to connect to IPC socket after multiple retries.',
      );
    }
    _ipcClient = null; // Ensure client is null if connection fails
  }

  void _setError(String message) {
    if (kDebugMode) {
      print(message);
    }
    _errorMessage.value = message;
    _playerState.value = PlayerState.error;
  }

  void _cleanupResources() {
    _stdoutSubscription?.cancel();
    _stdoutSubscription = null;

    _stderrSubscription?.cancel();
    _stderrSubscription = null;

    _watchdogTimer?.cancel();
    _watchdogTimer = null;

    // Disconnect IPC client if connected
    if (_ipcClient != null) {
      _ipcClient!.disconnect();
      _ipcClient = null;
    }

    // Clean up quality controller resources
    _qualityController.dispose();
  }

  Future<void> dispose() async {
    if (kDebugMode) {
      print('[MyPlayer] dispose() called');
    }
    _cleanupResources();

    if (_process != null) {
      try {
        // Try to gracefully terminate first using the IPC client
        if (_ipcClient != null) {
          _sendCommand('quit');
        } else {
          // Fallback to stdin if IPC client is not available
          try {
            _process!.stdin.writeln('quit');
          } catch (e) {
            if (kDebugMode) {
              print('Error sending quit command via stdin: $e');
            }
          }
        }

        // Give it a moment to quit gracefully
        await Future.delayed(const Duration(milliseconds: 500));

        // Force kill if still running
        if (_process != null) {
          try {
            _process!.kill();
          } catch (e) {
            if (kDebugMode) {
              print('Error killing process: $e');
            }
          } finally {
            _process = null; // Set to null after attempting to kill
          }
        }

        _id.value = 0;
        _playerState.value = PlayerState.closed;
      } catch (e) {
        if (kDebugMode) {
          print('Error in dispose: $e');
        }
        _process = null; // Set to null in case of other errors
      }
    } else {
      _playerState.value = PlayerState.closed;
    }
  }
}
