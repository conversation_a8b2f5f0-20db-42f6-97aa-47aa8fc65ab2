-- hls_quality_selector.lua
-- Simplified HLS track selector for MPV

local mp = require 'mp'
local utils = require 'mp.utils'
local assdraw = require 'mp.assdraw'

print("[QualitySelector] Script loaded as: " .. mp.get_script_name())

local showing_menu = false
local menu_type = "video"
local current_video_id = nil
local current_audio_id = nil

-- Get the current selected track IDs
local function update_current_tracks()
    current_video_id = mp.get_property_number("vid")
    current_audio_id = mp.get_property_number("aid")
    print(string.format("[QualitySelector] Current tracks - Video: %s, Audio: %s",
        tostring(current_video_id), tostring(current_audio_id)))
end

-- Format track information for display
local function format_video_track(track)
    -- Debug: Print the entire track table to see what's available
    print("[QualitySelector] Video track details:")
    for k, v in pairs(track) do
        print(string.format("[QualitySelector]   %s = %s", k, tostring(v)))
    end

    local title = track.title or ""

    -- Try to get dimensions directly from track properties first
    local w = track.demux_w or track.w or 0
    local h = track.demux_h or track.h or 0
    local fps = track.demux_fps or track.fps or 0
    local bitrate = track.demux_bitrate or track.bitrate or 0

    -- If dimensions not available in properties, try to extract from title
    -- Example: "h264 1920x1080 25 fps 2813 kbps"
    if w == 0 or h == 0 then
        w, h = title:match("(%d+)x(%d+)")
        w = tonumber(w) or 0
        h = tonumber(h) or 0
    end

    -- If bitrate not available in properties, try to extract from title
    if bitrate == 0 then
        local br = title:match("(%d+)%s*kbps")
        bitrate = tonumber(br) or 0
    end

    -- If fps not available in properties, try to extract from title
    if fps == 0 then
        local fp = title:match("(%d+)%s*fps")
        fps = tonumber(fp) or 0
    end

    -- Format the output
    local resolution = (w > 0 and h > 0) and string.format("%dx%d", w, h) or "unknown"
    local bitrate_str = (bitrate > 0) and string.format("%d kbps", bitrate) or "unknown bitrate"
    local fps_str = (fps > 0) and string.format("%d fps", fps) or ""

    -- If we have fps, include it in the description
    if fps_str ~= "" then
        return string.format("%s @ %s (%s)", resolution, bitrate_str, fps_str)
    else
        return string.format("%s @ %s", resolution, bitrate_str)
    end
end

local function format_audio_track(track)
    -- Debug: Print the entire track table to see what's available
    print("[QualitySelector] Audio track details:")
    for k, v in pairs(track) do
        print(string.format("[QualitySelector]   %s = %s", k, tostring(v)))
    end

    local title = track.title or ""

    -- Try to get properties directly from track first
    local codec = track.codec or title:match("^(%w+)") or "unknown"
    local channels = track.demux_channels or track.channels or 0
    local bitrate = track.demux_bitrate or track.bitrate or 0

    -- If channels not available in properties, try to extract from title
    if channels == 0 then
        local ch = title:match("(%d+)ch")
        channels = tonumber(ch) or 2  -- Default to 2 channels if not found
    end

    -- If bitrate not available in properties, try to extract from title
    if bitrate == 0 then
        local br = title:match("(%d+)%s*kbps")
        bitrate = tonumber(br) or 0
    end

    -- Format the output
    local channels_str = string.format("%dch", channels)
    local bitrate_str = (bitrate > 0) and string.format("%d kbps", bitrate) or "unknown bitrate"

    return string.format("%s @ %s", channels_str, bitrate_str)
end

-- Show the quality selection menu
local function show_menu(track_type)
    menu_type = track_type
    update_current_tracks()

    -- Get the track list from MPV
    local track_list = mp.get_property_native("track-list") or {}

    -- Debug: Print the entire track list
    print(string.format("[QualitySelector] Found %d total tracks", #track_list))

    local tracks = {}

    -- Filter tracks by type
    for i, track in ipairs(track_list) do
        print(string.format("[QualitySelector] Track %d: type=%s, id=%s, title=%s",
            i, track.type or "nil", tostring(track.id or "nil"), track.title or "nil"))

        if track.type == track_type and track.id and not track.external then
            local desc = ""
            local selected = (track_type == "video" and track.id == current_video_id) or
                            (track_type == "audio" and track.id == current_audio_id)

            if track_type == "video" then
                desc = format_video_track(track)
            else
                desc = format_audio_track(track)
            end

            table.insert(tracks, {
                id = track.id,
                desc = desc,
                selected = selected
            })
        end
    end

    -- Debug: Print what tracks we found
    print(string.format("[QualitySelector] Found %d %s tracks", #tracks, track_type))

    -- Debug: Print the tracks we're going to display
    for i, track in ipairs(tracks) do
        print(string.format("[QualitySelector] Menu item %d: id=%s, desc=%s, selected=%s",
            i, tostring(track.id), track.desc, tostring(track.selected)))
    end

    if #tracks == 0 then
        mp.osd_message("No " .. track_type .. " tracks found", 2)
        return
    end

    -- Create a styled OSD menu
    local ass = assdraw.ass_new()
    ass:new_event()
    ass:append("{\\fs28\\b1\\c&HFFFFFF&}")

    local title = (track_type == "video") and "Video Qualities" or "Audio Tracks"
    ass:append(title .. "\\N\\N")

    -- Reset style for menu items
    ass:append("{\\fs24\\b0\\c&HFFFFFF&}")

    for i, track in ipairs(tracks) do
        -- Add highlight for selected item
        if track.selected then
            ass:append("{\\b1\\c&H00FFFF&}")
        else
            ass:append("{\\b0\\c&HFFFFFF&}")
        end

        local selected_marker = track.selected and "●" or "○"
        local codec = menu_type == "video" and "h264" or "aac"
        ass:append(string.format("%d: %s [%s @ %s]\\N", i, selected_marker, codec, track.desc))

        -- Debug: Print each item
        local codec = menu_type == "video" and "h264" or "aac"
        print(string.format("[QualitySelector] Menu item %d: %s [%s @ %s] (id=%s)",
            i, selected_marker, codec, track.desc, tostring(track.id)))
    end

    -- Add instructions
    ass:append("\\N{\\fs20\\i1\\c&HAAAAAA&}Press number to switch")

    -- Show the menu
    mp.set_osd_ass(0, 0, ass.text)
    showing_menu = true

    -- Store tracks for selection
    mp.set_property_native("user-data/quality_selector_tracks", tracks)
end

-- Hide the menu
local function hide_menu()
    mp.set_osd_ass(0, 0, "")
    showing_menu = false
end

-- Switch to the selected quality
local function switch_quality(index)
    local tracks = mp.get_property_native("user-data/quality_selector_tracks") or {}

    print(string.format("[QualitySelector] Attempting to switch to %s track #%d", menu_type, index))

    if index < 1 or index > #tracks then
        print(string.format("[QualitySelector] Invalid selection: index %d out of range (1-%d)",
            index, #tracks))
        mp.osd_message("Invalid selection", 2)
        hide_menu()
        return
    end

    local selected = tracks[index]
    if selected then
        local prop = (menu_type == "video") and "vid" or "aid"
        local id = selected.id
        local codec = menu_type == "video" and "h264" or "aac"
        local desc_with_codec = string.format("[%s @ %s]", codec, selected.desc)

        print(string.format("[QualitySelector] Switching %s to track id %s: %s",
            menu_type, tostring(id), desc_with_codec))

        -- Set the property to change the track
        mp.set_property_number(prop, id)
        mp.osd_message("Switched to " .. desc_with_codec, 2)
    else
        print(string.format("[QualitySelector] Invalid selection: index %d not found in list", index))
        mp.osd_message("Invalid selection", 2)
    end

    hide_menu()
end

-- Key bindings 1–9 to switch
for i = 1, 9 do
    mp.add_key_binding(tostring(i), "select-track-" .. i, function()
        if showing_menu then switch_quality(i) end
    end)
end

-- Key bindings for menu
mp.add_key_binding("q", "show-video-menu", function() show_menu("video") end)
mp.add_key_binding("a", "show-audio-menu", function() show_menu("audio") end)
-- Add additional key bindings for better accessibility
mp.add_key_binding("Q", "show-video-menu-alt", function() show_menu("video") end)
mp.add_key_binding("A", "show-audio-menu-alt", function() show_menu("audio") end)

-- Directly select a quality from Flutter
mp.register_script_message("select-quality", function(type_and_index)
    local t, idx = type_and_index:match("^(%w+):(%d+)$")
    if t and idx then
        print(string.format("[Flutter] Requested to switch to %s track #%s", t, idx))
        menu_type = t
        switch_quality(tonumber(idx))
    else
        print("[Flutter] Invalid format for select-quality: use video:2 or audio:1")
    end
end)

-- Update tracks when track list changes
mp.observe_property("track-list", "native", function()
    update_current_tracks()
end)

-- Initialize when file is loaded
mp.register_event("file-loaded", function()
    update_current_tracks()
    print("[QualitySelector] Ready. Press 'q' or 'a' to view qualities.")
end)
