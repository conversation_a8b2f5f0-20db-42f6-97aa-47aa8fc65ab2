import 'package:flutter/material.dart';
import 'pages/home_page.dart';
import 'dart:io';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:flutter/foundation.dart';
import 'utils/mpv_initializer.dart';
import 'package:adblocker_webview/adblocker_webview.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (Platform.isWindows || Platform.isLinux) {
    sqfliteFfiInit();
    databaseFactory = databaseFactoryFfi;
  }

  // Initialize AdBlocker WebView
  try {
    await AdBlockerWebviewController.instance.initialize(
      FilterConfig(filterTypes: [FilterType.easyList, FilterType.adGuard]),
      [],
    );
    if (kDebugMode) {
      print('AdBlocker WebView initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing AdBlocker WebView: $e');
    }
  }

  // Initialize mpv for Windows
  if (Platform.isWindows) {
    try {
      await MpvInitializer.initialize();
      if (kDebugMode) {
        print('MPV initialized successfully at: ${MpvInitializer.mpvPath}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing MPV: $e');
      }
    }
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Cat Tv App',
      theme: ThemeData(
        primarySwatch: Colors.blueGrey,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.teal,
          brightness: Brightness.light,
        ),
        useMaterial3: true,
      ),
      home: const HomePage(),
    );
  }
}
