-- Flutter Quality Control Script for MPV
-- This script handles IPC communication with the Flutter app for quality control

local utils = require 'mp.utils'
local msg = require 'mp.msg'

-- Track information
local video_tracks = {}
local audio_tracks = {}
local current_video_id = nil
local current_audio_id = nil

-- Debug function
local function debug(text)
    msg.info(text)
    mp.osd_message(text, 1)
end

-- Update current track information
local function update_current_tracks()
    current_video_id = mp.get_property_number("vid")
    current_audio_id = mp.get_property_number("aid")
    msg.info(string.format("Current tracks - Video: %s, Audio: %s",
        tostring(current_video_id), tostring(current_audio_id)))
end

-- Format track information for Flutter
local function format_video_track(track)
    -- Debug: Print track details
    msg.debug("Video track details:")
    for k, v in pairs(track) do
        msg.debug(string.format("  %s = %s", k, tostring(v)))
    end

    local title = track.title or ""

    -- Try to get dimensions directly from track properties first
    local w = track.demux_w or track.w or 0
    local h = track.demux_h or track.h or 0
    local fps = track.demux_fps or track.fps or 0
    local bitrate = track.demux_bitrate or track.bitrate or 0

    -- If dimensions not available in properties, try to extract from title
    -- Example: "h264 1920x1080 25 fps 2813 kbps"
    if w == 0 or h == 0 then
        w, h = title:match("(%d+)x(%d+)")
        w = tonumber(w) or 0
        h = tonumber(h) or 0
    end

    -- If bitrate not available in properties, try to extract from title
    if bitrate == 0 then
        local br = title:match("(%d+)%s*kbps")
        bitrate = tonumber(br) or 0
    end

    -- If fps not available in properties, try to extract from title
    if fps == 0 then
        local fp = title:match("(%d+)%s*fps")
        fps = tonumber(fp) or 0
    end

    -- Format the output
    local resolution = (w > 0 and h > 0) and string.format("%dx%d", w, h) or "unknown"
    local bitrate_str = (bitrate > 0) and string.format("%d", bitrate) or "unknown"
    local fps_str = (fps > 0) and string.format("%d", fps) or "unknown"

    return {
        id = track.id,
        codec = track.codec or "h264",
        resolution = resolution,
        width = w,
        height = h,
        fps = fps_str,
        bitrate = bitrate_str,
        selected = (track.id == current_video_id)
    }
end

local function format_audio_track(track)
    -- Debug: Print track details
    msg.debug("Audio track details:")
    for k, v in pairs(track) do
        msg.debug(string.format("  %s = %s", k, tostring(v)))
    end

    local title = track.title or ""

    -- Try to get properties directly from track first
    local codec = track.codec or title:match("^(%w+)") or "unknown"
    local channels = track.demux_channels or track.channels or 0
    local bitrate = track.demux_bitrate or track.bitrate or 0

    -- If channels not available in properties, try to extract from title
    if channels == 0 then
        local ch = title:match("(%d+)ch")
        channels = tonumber(ch) or 2  -- Default to 2 channels if not found
    end

    -- If bitrate not available in properties, try to extract from title
    if bitrate == 0 then
        local br = title:match("(%d+)%s*kbps")
        bitrate = tonumber(br) or 0
    end

    return {
        id = track.id,
        codec = codec,
        channels = tostring(channels),
        bitrate = (bitrate > 0) and tostring(bitrate) or "unknown",
        selected = (track.id == current_audio_id)
    }
end

-- Get all available tracks and format them for Flutter
local function get_all_tracks()
    update_current_tracks()

    local track_list = mp.get_property_native("track-list") or {}
    local video_tracks = {}
    local audio_tracks = {}

    for _, track in ipairs(track_list) do
        if track.type == "video" and track.id and not track.external then
            table.insert(video_tracks, format_video_track(track))
        elseif track.type == "audio" and track.id and not track.external then
            table.insert(audio_tracks, format_audio_track(track))
        end
    end

    return {
        video = video_tracks,
        audio = audio_tracks
    }
end

-- Convert table to JSON string
local function table_to_json(t)
    local json = "{"
    local first = true

    for k, v in pairs(t) do
        if not first then json = json .. "," end
        first = false

        if type(k) == "number" then
            json = json .. '"' .. tostring(k) .. '":'
        else
            json = json .. '"' .. tostring(k) .. '":'
        end

        if type(v) == "table" then
            json = json .. table_to_json(v)
        elseif type(v) == "string" then
            json = json .. '"' .. v .. '"'
        elseif type(v) == "boolean" then
            json = json .. (v and "true" or "false")
        else
            json = json .. tostring(v)
        end
    end

    return json .. "}"
end

-- Handle commands from Flutter
local function handle_command(command, param)
    if command == "get_tracks" then
        local tracks = get_all_tracks()
        local json = table_to_json(tracks)
        msg.info("Sending tracks to Flutter: " .. json)
        return json
    elseif command == "set_video_track" then
        local id = tonumber(param)
        if id then
            mp.set_property_number("vid", id)
            msg.info("Set video track to: " .. tostring(id))
            return "ok"
        else
            msg.error("Invalid video track ID: " .. tostring(param))
            return "error"
        end
    elseif command == "set_audio_track" then
        local id = tonumber(param)
        if id then
            mp.set_property_number("aid", id)
            msg.info("Set audio track to: " .. tostring(id))
            return "ok"
        else
            msg.error("Invalid audio track ID: " .. tostring(param))
            return "error"
        end
    else
        msg.error("Unknown command: " .. tostring(command))
        return "unknown_command"
    end
end

-- Register script message to receive commands from Flutter
mp.register_script_message("flutter_command", function(command_string)
    msg.info("Received command: " .. command_string)

    local command, param = command_string:match("^([^:]+):?(.*)")
    if command then
        local result = handle_command(command, param)
        -- The result will be read by Flutter through stdout
        print("[FLUTTER_QUALITY_CONTROL] " .. result)
    else
        msg.error("Invalid command format: " .. command_string)
        print("[FLUTTER_QUALITY_CONTROL] error:invalid_format")
    end
end)

-- Send track information when file is loaded
mp.register_event("file-loaded", function()
    msg.info("File loaded, sending track information")
    local tracks = get_all_tracks()
    local json = table_to_json(tracks)
    print("[FLUTTER_QUALITY_CONTROL] tracks:" .. json)
end)

-- Update track information when track list changes
mp.observe_property("track-list", "native", function()
    msg.info("Track list changed, sending updated information")
    local tracks = get_all_tracks()
    local json = table_to_json(tracks)
    print("[FLUTTER_QUALITY_CONTROL] tracks:" .. json)
end)

msg.info("Flutter Quality Control script loaded")
print("[FLUTTER_QUALITY_CONTROL] ready")
