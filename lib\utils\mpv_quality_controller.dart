import 'dart:async';
import 'package:flutter/foundation.dart';

class VideoTrack {
  final int id;
  final String codec;
  final String resolution;
  final int width;
  final int height;
  final String fps;
  final String bitrate;
  final bool selected;

  VideoTrack({
    required this.id,
    required this.codec,
    required this.resolution,
    required this.width,
    required this.height,
    required this.fps,
    required this.bitrate,
    required this.selected,
  });

  factory VideoTrack.fromJson(Map<String, dynamic> json) {
    return VideoTrack(
      id: json['id'] as int,
      codec: json['codec'] as String? ?? 'unknown',
      resolution: json['resolution'] as String? ?? 'unknown',
      width: int.tryParse(json['width'].toString()) ?? 0,
      height: int.tryParse(json['height'].toString()) ?? 0,
      fps: json['fps'] as String? ?? 'unknown',
      bitrate: json['bitrate'] as String? ?? 'unknown',
      selected: json['selected'] as bool? ?? false,
    );
  }

  String get displayName => '$resolution @ $bitrate kbps';
}

class AudioTrack {
  final int id;
  final String codec;
  final String channels;
  final String bitrate;
  final bool selected;

  AudioTrack({
    required this.id,
    required this.codec,
    required this.channels,
    required this.bitrate,
    required this.selected,
  });

  factory AudioTrack.fromJson(Map<String, dynamic> json) {
    return AudioTrack(
      id: json['id'] as int,
      codec: json['codec'] as String? ?? 'unknown',
      channels: json['channels'] as String? ?? '2',
      bitrate: json['bitrate'] as String? ?? 'unknown',
      selected: json['selected'] as bool? ?? false,
    );
  }

  String get displayName => '$codec ${channels}ch @ $bitrate kbps';
}

class MpvQualityController {
  final List<VideoTrack> _videoTracks = [];
  final List<AudioTrack> _audioTracks = [];
  final StreamController<List<VideoTrack>> _videoTracksController =
      StreamController<List<VideoTrack>>.broadcast();
  final StreamController<List<AudioTrack>> _audioTracksController =
      StreamController<List<AudioTrack>>.broadcast();

  Stream<List<VideoTrack>> get videoTracksStream =>
      _videoTracksController.stream;
  Stream<List<AudioTrack>> get audioTracksStream =>
      _audioTracksController.stream;

  List<VideoTrack> get videoTracks => List.unmodifiable(_videoTracks);
  List<AudioTrack> get audioTracks => List.unmodifiable(_audioTracks);

  // Function to send to MPV player
  // Reference to the MpvOutputParser
  // This will be set by MyPlayer
  dynamic outputParser;

  final Function(String) _sendCommand;

  MpvQualityController(this._sendCommand);

  // This method is called by MpvOutputParser when a line is parsed
  void parseMpvOutput(String line) {
    // Check if the output parser has new tracks and update if it does
    if (outputParser != null && outputParser.hasNewTracks) {
      _updateTracksFromParser();
      outputParser.markTracksProcessed(); // Mark tracks as processed
    }

    // The 'ready' message is still useful to know when the script is loaded
    if (line.contains('[FLUTTER_QUALITY_CONTROL] ready')) {
      if (kDebugMode) {
        print('[MpvQualityController] Script is ready');
      }
      // Request tracks when script is ready - this command is still needed
      // to trigger the Lua script to send the initial track list.
      requestTracks();
    }
  }

  // Method to update tracks from the MpvOutputParser
  void _updateTracksFromParser() {
    if (outputParser == null) return;

    _videoTracks.clear();
    final List<Map<String, dynamic>> videoData = outputParser.videoTracksData;
    for (final track in videoData) {
      _videoTracks.add(VideoTrack.fromJson(track));
    }
    _videoTracksController.add(_videoTracks);

    if (kDebugMode) {
      print(
        '[MpvQualityController] Updated ${_videoTracks.length} video tracks from parser',
      );
    }

    _audioTracks.clear();
    final List<Map<String, dynamic>> audioData = outputParser.audioTracksData;
    for (final track in audioData) {
      _audioTracks.add(AudioTrack.fromJson(track));
    }
    _audioTracksController.add(_audioTracks);

    if (kDebugMode) {
      print(
        '[MpvQualityController] Updated ${_audioTracks.length} audio tracks from parser',
      );
    }
  }

  void requestTracks() {
    // This command is still needed to trigger the Lua script to send the initial track list
    _sendCommand('script-message flutter_command get_tracks');
    if (kDebugMode) {
      print('[MpvQualityController] Requested tracks from MPV via script');
    }
  }

  void setVideoTrack(int id) {
    _sendCommand('script-message flutter_command set_video_track:$id');
    if (kDebugMode) {
      print('[MpvQualityController] Set video track to $id via script');
    }
  }

  void setAudioTrack(int id) {
    _sendCommand('script-message flutter_command set_audio_track:$id');
    if (kDebugMode) {
      print('[MpvQualityController] Set audio track to $id via script');
    }
  }

  void dispose() {
    _videoTracksController.close();
    _audioTracksController.close();
  }
}
