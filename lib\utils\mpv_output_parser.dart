import 'package:flutter/foundation.dart';
import 'dart:convert';
import 'mpv_quality_controller.dart';

class MpvOutputParser {
  final List<Map<String, dynamic>> _videoTracks = [];
  final List<Map<String, dynamic>> _audioTracks = [];
  bool _hasNewTracks = false;

  // Reference to the quality controller
  MpvQualityController? qualityController;

  // Add getters for the internal track lists
  List<Map<String, dynamic>> get videoTracksData => _videoTracks;
  List<Map<String, dynamic>> get audioTracksData => _audioTracks;

  bool get hasNewTracks => _hasNewTracks;

  void parseLine(String line) {
    // Forward to quality controller if available
    if (qualityController != null) {
      qualityController!.parseMpvOutput(line);
    }
    // Check for video track information in the format: "○ Video  --vid=1  (h264 426x240 25 fps 690 kbps)"
    final videoMatch = RegExp(
      r'[○●]\s+Video\s+--vid=(\d+)\s+\((.*?)\)',
    ).firstMatch(line);
    if (videoMatch != null) {
      final id = int.parse(videoMatch.group(1)!);
      final desc = videoMatch.group(2)!.trim();

      // Parse resolution, fps, and bitrate from the description
      final resolutionMatch = RegExp(r'(\d+x\d+)').firstMatch(desc);
      final fpsMatch = RegExp(r'(\d+)\s+fps').firstMatch(desc);
      final bitrateMatch = RegExp(r'(\d+)\s+kbps').firstMatch(desc);

      final resolution = resolutionMatch?.group(1) ?? 'unknown';
      final fps = fpsMatch?.group(1) ?? 'unknown';
      final bitrate = bitrateMatch?.group(1) ?? 'unknown';

      final trackInfo = {
        'id': id,
        'desc': '$resolution @ $bitrate kbps',
        'resolution': resolution,
        'fps': fps,
        'bitrate': bitrate,
        'selected': line.startsWith('●'),
      };

      // Check if this track is already in the list
      final existingIndex = _videoTracks.indexWhere((t) => t['id'] == id);
      if (existingIndex >= 0) {
        _videoTracks[existingIndex] = trackInfo;
      } else {
        _videoTracks.add(trackInfo);
        _hasNewTracks = true;
      }

      if (kDebugMode) {
        print('[MpvOutputParser] Parsed Video Track: ${jsonEncode(trackInfo)}');
      }
      return;
    }

    // Check for audio track information in the format: "○ Audio  --aid=1  (aac 2ch 48000 Hz 690 kbps)"
    final audioMatch = RegExp(
      r'[○●]\s+Audio\s+--aid=(\d+)\s+\((.*?)\)',
    ).firstMatch(line);
    if (audioMatch != null) {
      final id = int.parse(audioMatch.group(1)!);
      final desc = audioMatch.group(2)!.trim();

      // Parse codec, channels, and bitrate from the description
      final codecMatch = RegExp(r'^(\w+)').firstMatch(desc);
      final channelsMatch = RegExp(r'(\d+)ch').firstMatch(desc);
      final bitrateMatch = RegExp(r'(\d+)\s+kbps').firstMatch(desc);

      final codec = codecMatch?.group(1) ?? 'unknown';
      final channels = channelsMatch?.group(1) ?? 'unknown';
      final bitrate = bitrateMatch?.group(1) ?? 'unknown';

      final trackInfo = {
        'id': id,
        'desc': '$codec ${channels}ch @ $bitrate kbps',
        'codec': codec,
        'channels': channels,
        'bitrate': bitrate,
        'selected': line.startsWith('●'),
      };

      // Check if this track is already in the list
      final existingIndex = _audioTracks.indexWhere((t) => t['id'] == id);
      if (existingIndex >= 0) {
        _audioTracks[existingIndex] = trackInfo;
      } else {
        _audioTracks.add(trackInfo);
        _hasNewTracks = true;
      }

      if (kDebugMode) {
        print('[MpvOutputParser] Parsed Audio Track: ${jsonEncode(trackInfo)}');
      }
      return;
    }

    // Fallback to the original parsing logic for compatibility
    final oldVideoMatch = RegExp(r'--vid\s*(\d+):\s*(.*)').firstMatch(line);
    if (oldVideoMatch != null) {
      final id = int.parse(oldVideoMatch.group(1)!);
      final desc = oldVideoMatch.group(2)!.trim();

      // Check if this track is already in the list
      final existingIndex = _videoTracks.indexWhere((t) => t['id'] == id);
      if (existingIndex < 0) {
        _videoTracks.add({'id': id, 'desc': desc});
        _hasNewTracks = true;
        if (kDebugMode) {
          print(
            '[MpvOutputParser] Parsed Video Track (legacy): id=$id | desc=$desc',
          );
        }
      }
      return;
    }

    final oldAudioMatch = RegExp(r'--aid\s*(\d+):\s*(.*)').firstMatch(line);
    if (oldAudioMatch != null) {
      final id = int.parse(oldAudioMatch.group(1)!);
      final desc = oldAudioMatch.group(2)!.trim();

      // Check if this track is already in the list
      final existingIndex = _audioTracks.indexWhere((t) => t['id'] == id);
      if (existingIndex < 0) {
        _audioTracks.add({'id': id, 'desc': desc});
        _hasNewTracks = true;
        if (kDebugMode) {
          print(
            '[MpvOutputParser] Parsed Audio Track (legacy): id=$id | desc=$desc',
          );
        }
      }
      return;
    }
  }

  // Get tracks as a JSON string
  String getTracksJson() {
    final tracksData = {
      'videoTracks': _videoTracks,
      'audioTracks': _audioTracks,
    };
    return jsonEncode(tracksData);
  }

  void markTracksProcessed() {
    _hasNewTracks = false;
  }

  void reset() {
    _videoTracks.clear();
    _audioTracks.clear();
    _hasNewTracks = false;
    if (kDebugMode) {
      print('[MpvOutputParser] Parser reset.');
    }
  }

  void setQualityController(MpvQualityController controller) {
    qualityController = controller;
    if (kDebugMode) {
      print('[MpvOutputParser] Quality controller set.');
    }
  }
}
