import 'dart:io';

class MpvConfig {
  /// Get optimized MPV configuration for streaming
  static Map<String, String> getOptimizedConfig() {
    final config = <String, String>{
      // Basic UI settings
      'osc': 'yes', // Enable default on-screen controls
      'title': _getWindowTitle(), // Custom window title
      // Hardware acceleration
      'hwdec': 'auto', // Use hardware decoding when available
      // Network and buffering settings
      'cache': 'yes', // Enable cache
      'cache-secs': '30', // Cache 30 seconds
      // Video output settings
      'vo': 'gpu', // Use GPU-accelerated output
      // Stream specific settings
      'ytdl': 'yes', // Enable youtube-dl for URL parsing
      // Window settings
      'ontop': 'yes', // Keep window on top
      'force-window': 'yes', // Force window creation
      'keep-open': 'yes', // Keep the player open when the file ends
      // UI settings for quality menu
      'osd-font-size': '24', // Font size for on-screen display
      'osd-color':
          '#FFFFFF', // Color for on-screen display (must include # prefix)
      'osd-border-size': '1', // Border size for on-screen display
      'osd-border-color':
          '#000000', // Border color for on-screen display (must include # prefix)
    };

    // Scripts are loaded automatically from the script-dir
    // No need to explicitly list them with the 'script' option.

    // Enable IPC socket for communication
    // Use a named pipe on Windows
    config['input-ipc-server'] = r'\\.\pipe\cat_tv_mpv_ipc';

    // Add platform-specific settings
    if (Platform.isWindows) {
      config.addAll({
        'priority': 'high', // Set process priority to high
      });
    }

    return config;
  }

  /// Convert config map to command line arguments
  static List<String> toArguments(Map<String, String> config) {
    final args = <String>[];
    config.forEach((key, value) {
      args.add('--$key=$value');
    });
    return args;
  }

  /// Get a custom window title for the MPV player
  static String _getWindowTitle() {
    return 'Cat TV Player';
  }
}
