import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class MpvInitializer {
  static String? _mpvPath;
  static String? _mpvScriptsDir;
  static final Map<String, String> _scriptPaths = {};

  static String? get mpvPath => _mpvPath;
  static String? get mpvScriptsDir => _mpvScriptsDir;
  static Map<String, String> get scriptPaths => Map.unmodifiable(_scriptPaths);

  static Future<void> initialize() async {
    if (!Platform.isWindows) return;

    try {
      // Get the application documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final mpvDir = Directory(path.join(appDocDir.path, 'cat_tv', 'mpv'));

      // Create the directory if it doesn't exist
      if (!await mpvDir.exists()) {
        await mpvDir.create(recursive: true);
      }

      // Set the scripts directory to be the same as the MPV directory
      _mpvScriptsDir = mpvDir.path;

      // Define the path for the mpv executable
      _mpvPath = path.join(mpvDir.path, 'mpv.exe');
      final mpvFile = File(_mpvPath!);

      // Check if mpv.exe already exists in the user directory
      if (!await mpvFile.exists()) {
        if (kDebugMode) {
          print('Copying mpv.exe to: $_mpvPath');
        }

        try {
          // Copy mpv.exe from assets to the user directory
          final byteData = await rootBundle.load('assets/mpv/mpv.exe');
          final buffer = byteData.buffer.asUint8List();
          await mpvFile.writeAsBytes(buffer, flush: true);

          // Verify the file was copied successfully
          if (!await mpvFile.exists() || await mpvFile.length() == 0) {
            throw Exception('Failed to copy mpv.exe or file is empty');
          }

          if (kDebugMode) {
            print(
              'Successfully copied mpv.exe (${await mpvFile.length()} bytes)',
            );
          }
        } catch (e) {
          if (kDebugMode) {
            print('Error copying mpv.exe: $e');
          }
          _mpvPath = null;
          rethrow;
        }
      } else {
        if (kDebugMode) {
          print(
            'mpv.exe already exists at: $_mpvPath (${await mpvFile.length()} bytes)',
          );
        }
      }

      // Copy scripts from assets to the MPV directory
      await _copyScriptsFromAssets(mpvDir.path);
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing mpv for Windows: $e');
      }
      _mpvPath = null;
    }
  }

  /// Copy all script files from assets to the user's scripts directory
  static Future<void> _copyScriptsFromAssets(String mpvDir) async {
    try {
      // List of files to copy directly to mpv directory (excluding scripts)
      final mainFiles = ['input.conf'];

      // Copy main files to mpv directory
      for (final file in mainFiles) {
        await _copyAssetToUserDir(file, mpvDir);
      }

      // Define the path for the scripts directory in assets
      const assetScriptsDir = 'assets/mpv/scripts';

      // Define the destination path for the scripts directory in the user directory
      final destScriptsDir = Directory(path.join(mpvDir, 'scripts'));

      // Create the destination scripts directory if it doesn't exist
      if (!await destScriptsDir.exists()) {
        await destScriptsDir.create(recursive: true);
      }

      // List all files in the assets scripts directory
      final manifest = await rootBundle.loadString('AssetManifest.json');
      final Map<String, dynamic> assetMap = json.decode(manifest);
      final scriptAssets =
          assetMap.keys
              .where(
                (key) =>
                    key.startsWith(assetScriptsDir) && key.endsWith('.lua'),
              )
              .toList();

      // Copy each script file from assets to the user's scripts directory
      for (final scriptAssetPath in scriptAssets) {
        final fileName = path.basename(scriptAssetPath);
        await _copyAssetToUserDir(
          fileName,
          destScriptsDir.path,
          assetPath: scriptAssetPath,
        );
      }

      // Set the scripts directory path
      _mpvScriptsDir = destScriptsDir.path;
    } catch (e) {
      if (kDebugMode) {
        print('Error copying files: $e');
      }
    }
  }

  /// Helper method to copy an asset file to the user directory
  static Future<void> _copyAssetToUserDir(
    String fileName,
    String destDir, {
    String? assetPath,
  }) async {
    final destPath = path.join(destDir, fileName);
    final destFile = File(destPath);
    final sourceAssetPath = assetPath ?? 'assets/mpv/$fileName';

    // Check if file already exists
    if (await destFile.exists()) {
      if (kDebugMode) {
        print('File already exists, skipping copy: $fileName at $destPath');
      }
      // Store the script path even if we didn't copy
      if (fileName.endsWith('.lua')) {
        _scriptPaths[fileName] = destPath;
      }
      return; // Skip copying if file exists
    }

    try {
      // Check if asset exists before trying to load it
      await rootBundle.load(sourceAssetPath);

      if (kDebugMode) {
        print('Copying file: $fileName to $destPath');
      }

      // Copy file from assets to destination directory
      final byteData = await rootBundle.load(sourceAssetPath);
      final buffer = byteData.buffer.asUint8List();
      await destFile.writeAsBytes(buffer, flush: true);

      // Store the script path for later use if it's a script
      if (fileName.endsWith('.lua')) {
        _scriptPaths[fileName] = destPath;
      }

      if (kDebugMode) {
        print('Successfully copied file: $fileName to $destPath');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error copying file $fileName: $e');
      }
    }
  }
}
