<!DOCTYPE html>
<html>
  <head>
    <title>Shaka Player</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/shaka-player/4.3.0/shaka-player.ui.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/shaka-player/4.3.0/controls.min.css"></script>
    <style>
      html,
      body {
        margin: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background-color: #000;
      }
      #video-container {
        width: 100%;
        height: 100%;
        position: relative;
      }
      video {
        width: 100%;
        height: 100%;
      }
      .input-container {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 10;
        display: flex;
        gap: 10px;
      }
      input {
        width: 300px;
        padding: 5px;
      }
      button {
        padding: 5px 10px;
      }
    </style>
  </head>
  <body>
    <div class="input-container">
      <input
        type="text"
        id="manifest-url"
        value="https://ssc-news-live-enc.edgenextcdn.net/out/v1/ef466f43623c4bbaa3f905b566ec35ea/index.mpd"
        placeholder="Enter Manifest or MPD URL"
      />
      <button id="load-button">Load Stream</button>
    </div>
    <div id="video-container" data-shaka-player-container>
      <video autoplay data-shaka-player id="video"></video>
    </div>

    <script>
      const video = document.getElementById('video')
      const videoContainer = document.getElementById('video-container')
      const manifestUrlInput = document.getElementById('manifest-url')
      const loadButton = document.getElementById('load-button')

      let player

      async function initApp() {
        shaka.log.setLevel(shaka.log.Level.DEBUG)
        shaka.ui.Overlay.registerElement('input-container', videoContainer)

        const ui = videoContainer['ui']
        const controls = ui.getControls()
        player = controls.getPlayer()

        player.addEventListener('error', onErrorEvent)

        // Configure DRM if necessary
        const clearKeys = {
          // KID
          '6563393466393230616661353862633463653962306661636131376634393436':
            '39653037393264363039396139646135', // KEY
        }

        player.configure({
          drm: {
            clearKeys: clearKeys,
            servers: {
              'com.widevine.alpha': 'YOUR_WIDEVINE_LICENSE_URL', // Shaka Player might derive this from PSSH, keep placeholder for now
              'com.microsoft.playready':
                'https://shahid.la.drm.cloud/acquire-license/playready?BrandGuid=2be49af0-6fbd-4511-8e11-3d6523185bb4',
              // Add other DRM systems as needed
            },
          },
        })

        console.log('Shaka Player configuration:', player.getConfiguration())

        // Load the default stream immediately
        loadStream()

        loadButton.addEventListener('click', loadStream)

        // Listen for messages from the Flutter app
        window.addEventListener('message', event => {
          console.log('Message received from Flutter:', event.data)
          if (
            event.data &&
            event.data.type === 'loadStream' &&
            event.data.url
          ) {
            manifestUrlInput.value = event.data.url
            console.log('Manifest URL set to:', manifestUrlInput.value)
            loadStream()
          }
        })
      }

      async function loadStream() {
        const manifestUri = manifestUrlInput.value
        if (!manifestUri) {
          console.error('No manifest URL provided.')
          return
        }
        try {
          await player.load(manifestUri)
          console.log('The video has now been loaded!')
        } catch (error) {
          onError(error)
        }
      }

      function onErrorEvent(event) {
        onError(event.detail)
      }

      function onError(error) {
        // Log the full error object for detailed debugging
        console.error('Shaka Player Error:', {
          code: error.code,
          severity: error.severity,
          category: error.category,
          message: error.message,
          data: error.data,
          fullError: error,
        })
      }

      document.addEventListener('DOMContentLoaded', initApp)
    </script>
  </body>
</html>
