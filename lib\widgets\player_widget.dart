import 'dart:io';
import 'package:cat_tv/player/my_player.dart';
import 'package:flutter/material.dart';
import 'package:cat_tv/widgets/quality_selector.dart';
import 'package:cat_tv/widgets/quality_display_widget.dart';

class PlayerWidget extends StatefulWidget {
  final MyPlayer player;

  const PlayerWidget({super.key, required this.player});

  @override
  State<PlayerWidget> createState() => _PlayerWidgetState();
}

class _PlayerWidgetState extends State<PlayerWidget> {
  bool _showQualitySelector = false;

  void _toggleQualitySelector() {
    setState(() {
      _showQualitySelector = !_showQualitySelector;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        _buildPlayerUI(),
        if (_showQualitySelector)
          Positioned.fill(
            child: Center(
              child: QualitySelector(
                controller: widget.player.qualityController,
                onClose: _toggleQualitySelector,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildPlayerUI() {
    return ValueListenableBuilder<PlayerState>(
      valueListenable: widget.player.playerState,
      builder: (context, playerState, _) {
        return ValueListenableBuilder<String?>(
          valueListenable: widget.player.errorMessage,
          builder: (context, errorMessage, _) {
            return ValueListenableBuilder<int>(
              valueListenable: widget.player.id,
              builder: (context, textureId, _) {
                // Show different UI based on player state
                switch (playerState) {
                  case PlayerState.idle:
                    return const Center(
                      child: Text(
                        'Ready to play',
                        style: TextStyle(fontSize: 16),
                      ),
                    );

                  case PlayerState.loading:
                    return const Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircularProgressIndicator(),
                          SizedBox(height: 16),
                          Text(
                            'Loading stream...',
                            style: TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    );

                  case PlayerState.playing:
                    if (Platform.isWindows) {
                      return Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.play_circle_outline,
                              size: 48,
                              color: Colors.green,
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'Playing with external MPV player',
                              textAlign: TextAlign.center,
                              style: TextStyle(fontSize: 16),
                            ),
                            const SizedBox(height: 8),
                            QualityDisplayWidget(
                              qualityController:
                                  widget.player.qualityController,
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ElevatedButton.icon(
                                  icon: const Icon(Icons.settings),
                                  label: const Text('Quality'),
                                  onPressed: _toggleQualitySelector,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.blue,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                ElevatedButton.icon(
                                  icon: const Icon(Icons.stop),
                                  label: const Text('Stop'),
                                  onPressed: () {
                                    widget.player.dispose();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.red,
                                    foregroundColor: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      );
                    } else {
                      // For other platforms, use the texture-based approach
                      final videoParams = widget.player.videoParams.value;
                      final aspectRatio =
                          videoParams.dh != 0
                              ? videoParams.dw / videoParams.dh
                              : 16 / 9; // default fallback
                      return Center(
                        child: AspectRatio(
                          aspectRatio: aspectRatio,
                          child: Texture(textureId: textureId),
                        ),
                      );
                    }

                  case PlayerState.error:
                    return Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.error_outline,
                            size: 48,
                            color: Colors.red,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Error playing stream',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          if (errorMessage != null)
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                              ),
                              child: Text(
                                errorMessage,
                                textAlign: TextAlign.center,
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            icon: const Icon(Icons.refresh),
                            label: const Text('Try Again'),
                            onPressed: () {
                              // Get the current context's Navigator
                              Navigator.of(context).pop();
                            },
                          ),
                        ],
                      ),
                    );

                  case PlayerState.closed:
                    return Center(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.stop_circle_outlined,
                            size: 48,
                            color: Colors.grey,
                          ),
                          const SizedBox(height: 16),
                          const Text(
                            'Playback stopped',
                            style: TextStyle(fontSize: 16),
                          ),
                          const SizedBox(height: 16),
                          ElevatedButton.icon(
                            icon: const Icon(Icons.arrow_back),
                            label: const Text('Back to Channels'),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                          ),
                        ],
                      ),
                    );
                }
              },
            );
          },
        );
      },
    );
  }
}
